name: ${CUSTOMER_NAME}

services:
  # Netzwerk-Container (muss zuerst definiert werden, da andere Services davon abhängen)
  network-container:
    image: alpine:latest
    container_name: ${CUSTOMER_NAME}-network
    command: tail -f /dev/null
    restart: unless-stopped
    mem_limit: 256m
    networks:
      customer_macvlan:
        ipv4_address: ${CUSTOMER_IP}
  # Redis Cache Server
  redis:
    image: redis:alpine
    container_name: ${CUSTOMER_NAME}-redis
    restart: unless-stopped
    volumes:
      - redis-data:/data
    command: redis-server --appendonly yes
    network_mode: "service:network-container"
    mem_limit: 256m
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 3
    depends_on:
      - network-container
  # Nextcloud
  nextcloud:
    image: nextcloud:latest
    container_name: ${CUSTOMER_NAME}-nextcloud
    restart: unless-stopped
    volumes:
      - nextcloud-html:/var/www/html
      - nextcloud-data:/var/www/html/data
      - nextcloud-custom-apps:/var/www/html/custom_apps
      - nextcloud-config:/var/www/html/config
      - ssl-certs:/etc/ssl/certs:ro
      - ssl-private:/etc/ssl/private:ro
      - nextcloud-templates:/var/www/html/themes/leos360
      - nextcloud-setup:/docker-entrypoint-hooks.d/post-installation
    environment:
      - POSTGRES_HOST=${EXTERNAL_DB_HOST}
      - POSTGRES_DB=${EXTERNAL_NEXTCLOUD_DB}
      - POSTGRES_USER=${EXTERNAL_DB_USER}
      - POSTGRES_PASSWORD=${EXTERNAL_DB_PASSWORD}
      - NEXTCLOUD_ADMIN_USER=${NEXTCLOUD_ADMIN_USER}
      - NEXTCLOUD_ADMIN_PASSWORD=${NEXTCLOUD_ADMIN_PASSWORD}
      - NEXTCLOUD_TRUSTED_DOMAINS=${NEXTCLOUD_TRUSTED_DOMAINS}
      - NEXTCLOUD_OVERWRITEHOST=${NEXTCLOUD_OVERWRITEHOST}
      - OVERWRITEPROTOCOL=https
      - OVERWRITEWEBROOT=/
      - PHP_MEMORY_LIMIT=512M
      - PHP_UPLOAD_LIMnIT=10G
      # Redis-Konfiguration
      - REDIS_HOST=localhost
      - REDIS_HOST_PORT=6379
      - REDIS_HOST_DB=1
      - REDIS_HOST_TIMEOUT=0.5
      # Sprach- und Regioneinstellungen
      - NEXTCLOUD_DEFAULT_LANGUAGE=de
      - NEXTCLOUD_LOCALE=de_DE
      - NEXTCLOUD_PHONE_REGION=AT
      # Zeitzonen-Einstellung
      - TZ=Europe/Vienna
      # Theming (grundlegende Einstellungen)
      - NEXTCLOUD_NAME=LEOS 360
      - NEXTCLOUD_URL=https://www.leos360.com
      - NEXTCLOUD_SLOGAN=Business Cloud
      # SMTP
      - SMTP_HOST=127.0.0.1
      - SMTP_PORT=25
      - SMTP_DOMAIN=mail.${CUSTOMER_DOMAIN}
      - MAIL_FROM_ADDRESS=noreply
      - MAIL_DOMAIN=${CUSTOMER_DOMAIN}
      - NEXTCLOUD_UPDATE=1
    # Nutze das Netzwerk des network-containers
    network_mode: "service:network-container"
    mem_limit: 2g
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost/status.php"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 60s
    depends_on:
      - network-container
      - redis

  # Keycloak SSO
  keycloak:
    image: leos360-keycloak:latest
    container_name: ${CUSTOMER_NAME}-keycloak
    volumes:
      # Keycloak data and configuration
      - keycloak-data:/opt/keycloak/data
      - keycloak-config:/opt/keycloak/data/import
      # SSL certificates
      - ssl-certs:/etc/ssl/certs:ro
      - ssl-private:/etc/ssl/private:ro
    environment:
      # Database connection settings
      - KC_DB_URL=jdbc:postgresql://${KEYCLOAK_DATABASE_HOST}:${KEYCLOAK_DATABASE_PORT}/${KEYCLOAK_DATABASE_NAME}
      - KC_DB_USERNAME=${KEYCLOAK_DATABASE_USER}
      - KC_DB_PASSWORD=${KEYCLOAK_DATABASE_PASSWORD}
      # Instance configuration
      - KC_HOSTNAME=${KC_HOSTNAME}
      # Admin user bootstrap (fixed incorrect variable names)
      - KC_BOOTSTRAP_ADMIN_USERNAME=${KEYCLOAK_ADMIN_USERNAME}
      - KC_BOOTSTRAP_ADMIN_PASSWORD=${KEYCLOAK_ADMIN_PASSWORD}
      # Proxy configuration for nginx
      - KC_PROXY=edge
      # Start command options
      - KC_HTTP_ENABLED=true
      - KC_HTTP_PORT=8080
      - KC_HTTP_MANAGEMENT_PORT=9001
      - KC_HEALTH_ENABLED=true
      # JVM heap settings: 50% initial, max 70% of available RAM
      - JAVA_OPTS_KC_HEAP=-XX:MaxRAMPercentage=70 -XX:InitialRAMPercentage=50
      - KC_OVERRIDE=true
    # Use the network of the network-container
    network_mode: "service:network-container"
    expose:
      - "8080"  # Main Keycloak HTTP port
      - "9001"  # Management/Health port
    healthcheck:
      test: ["CMD", "curl", "-f", "http://${CUSTOMER_IP}:9001/health/ready"]
      interval: 10s
      timeout: 5s
      retries: 3
      start_period: 90s
    restart: unless-stopped
    mem_limit: 2g
    depends_on:
      - network-container
      - lldap

  # LLDAP - Lightweight LDAP Server
  lldap:
    image: lldap/lldap:stable
    container_name: ${CUSTOMER_NAME}-lldap
    volumes:
      # Persistent storage and configuration
      - lldap-data:/data
      - ssl-certs:/etc/ssl/certs:ro
      - ssl-private:/etc/ssl/private:ro
      - lldap-bootstrap:/bootstrap
    environment:
      # User and group IDs for container
      - UID=${LLDAP_UID:-1000}
      - GID=${LLDAP_GID:-1000}
      - TZ=${TIMEZONE}
      # Security settings
      - LLDAP_JWT_SECRET=${LLDAP_JWT_SECRET}
      - LLDAP_KEY_SEED=${LLDAP_KEY_SEED}
      # LDAP configuration
      - LLDAP_LDAP_BASE_DN=${LLDAP_BASE_DN}
      - LLDAP_LDAP_USER_PASS=${LLDAP_ADMIN_PASS}
      # Database connection
      - LLDAP_DATABASE_URL=postgres://${EXTERNAL_DB_USER}:${EXTERNAL_DB_PASSWORD}@************:5432/${EXTERNAL_LLDAP_DB}
      # LDAPS (LDAP over SSL) configuration
      - LLDAP_LDAPS_OPTIONS__ENABLED=true
      - LLDAP_LDAPS_OPTIONS__CERT_FILE=/etc/ssl/certs/star_leos360_cloud.crt
      - LLDAP_LDAPS_OPTIONS__KEY_FILE=/etc/ssl/private/star_leos360_cloud.key
      # Bootstrap configuration
      - LLDAP_URL=http://localhost:17170
      - LLDAP_ADMIN_USERNAME=admin
      - LLDAP_ADMIN_PASSWORD=${LLDAP_ADMIN_PASS}
      - USER_CONFIGS_DIR=/bootstrap/user-configs
      - GROUP_CONFIGS_DIR=/bootstrap/group-configs
      - USER_SCHEMAS_DIR=/bootstrap/user-schemas
      - GROUP_SCHEMAS_DIR=/bootstrap/group-schemas
      - LLDAP_SET_PASSWORD_PATH=/app/lldap_set_password
      - DO_CLEANUP=false  
    # Nutze das Netzwerk des network-containers
    network_mode: "service:network-container"
    restart: unless-stopped
    mem_limit: 2g
    depends_on:
      - network-container

  # Postfix - Mail Transfer Agent (MTA)
  postfix:
    image: boky/postfix:latest
    container_name: ${CUSTOMER_NAME}-postfix
    restart: unless-stopped
    environment:
      # Mail domain configuration
      - DOMAIN=${CUSTOMER_DOMAIN}
      - ALLOWED_SENDER_DOMAINS=${CUSTOMER_DOMAIN}
      - RELAYHOST=${RELAYHOST}
    volumes:
      # Configuration and data storage
      - postfix-config:/etc/postfix
      - ssl-certs:/etc/ssl/certs:ro
      - ssl-private:/etc/ssl/private:ro
      - postfix-spool:/var/spool/postfix
    # Nutze das Netzwerk des network-containers
    network_mode: "service:network-container"
    mem_limit: 512m
    depends_on:
      - network-container
      - lldap

  # Dovecot - IMAP/POP3 Server
  dovecot:
    image: dovecot/dovecot:2.3
    container_name: ${CUSTOMER_NAME}-dovecot
    restart: unless-stopped
    volumes:
      # Configuration files
      - dovecot-config:/etc/dovecot
      - dovecot-config-d:/etc/dovecot/conf.d
      - dovecot-ssl:/usr/share/dovecot
      - ssl-certs:/etc/ssl/certs:ro
      - ssl-private:/etc/ssl/private:ro
      # Mail storage
      - mail-data:/var/mail
      - postfix-spool:/var/spool/postfix
    environment:
      - DOMAIN=${CUSTOMER_DOMAIN}
    # Nutze das Netzwerk des network-containers
    network_mode: "service:network-container"
    mem_limit: 512m
    depends_on:
      - network-container
      - lldap

# Named volumes for persistent data storage
volumes:
  # Redis data storage
  redis-data:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/redis/data
      o: bind
  # SSL certificates
  ssl-certs:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/ssl
      o: bind
  ssl-private:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/ssl
      o: bind

  # Nextcloud data storage
  nextcloud-data:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/nextcloud/data
      o: bind
  nextcloud-html:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/nextcloud/html
      o: bind
  nextcloud-custom-apps:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/nextcloud/custom_apps
      o: bind
  nextcloud-config:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/nextcloud/config
      o: bind
  nextcloud-templates:
    driver: local
    driver_opts:
      type: none
      device: /mnt/storage/nextcloud
      o: bind
  nextcloud-setup:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/nextcloud/setup
      o: bind
  # Keycloak data storage
  keycloak-data:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/keycloak/data
      o: bind
  keycloak-config:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/keycloak/config
      o: bind

  # LLDAP data and configuration
  lldap-data:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/lldap/data
      o: bind
  lldap-bootstrap:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/lldap/config/bootstrap
      o: bind

  # Postfix configuration and data
  postfix-config:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/postfix/config
      o: bind
  postfix-spool:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/postfix/data/spool
      o: bind

  # Dovecot configuration and data
  dovecot-config:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/dovecot/config
      o: bind
  dovecot-config-d:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/dovecot/config/conf.d
      o: bind
  dovecot-ssl:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/dovecot/config/ssl
      o: bind
  mail-data:
    driver: local
    driver_opts:
      type: none
      device: ${CONFIG_BASE}/dovecot/data/mail
      o: bind

networks:
  customer_macvlan:
    external: true
    name: shared_customer_network